#!/usr/bin/env python3
"""
Final comprehensive test for follow-up question functionality
"""

import sys
import os
import asyncio
from datetime import datetime
from bson import ObjectId

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_workflow():
    """Test the complete follow-up question workflow"""
    print("🚀 Final Follow-up Question Workflow Test")
    print("=" * 60)
    
    try:
        # Import all required functions
        from main3 import (
            extract_followup_question,
            store_followup_question,
            get_last_followup,
            mark_followup_answered,
            is_followup_acceptance
        )
        
        # Test data
        user_id = str(ObjectId())
        chat_id = str(ObjectId())
        
        print(f"👤 User ID: {user_id}")
        print(f"💬 Chat ID: {chat_id}")
        print()
        
        # Scenario 1: User asks about AI
        print("📝 Scenario 1: User asks 'What is artificial intelligence?'")
        
        # Simulate LLM response with follow-up
        ai_response = """
        Artificial Intelligence (AI) is a branch of computer science that aims to create machines capable of performing tasks that typically require human intelligence. This includes learning, reasoning, problem-solving, perception, and language understanding.

        AI can be categorized into:
        • Narrow AI: Designed for specific tasks (like voice assistants)
        • General AI: Hypothetical AI with human-level intelligence across all domains
        • Superintelligence: AI that surpasses human intelligence

        👉 Would you like me to explain more about machine learning algorithms and how they work?
        """
        
        # Extract follow-up
        followup1 = extract_followup_question(ai_response)
        print(f"🔍 Extracted follow-up: '{followup1}'")
        
        # Store follow-up
        store_followup_question(user_id, chat_id, followup1)
        print("💾 Follow-up stored successfully")
        
        # Verify storage
        retrieved1 = get_last_followup(user_id, chat_id)
        print(f"📖 Retrieved follow-up: '{retrieved1}'")
        assert retrieved1 == followup1, "Follow-up storage/retrieval failed"
        print("✅ Storage verification passed")
        print()
        
        # Scenario 2: User accepts follow-up
        print("📝 Scenario 2: User responds 'yes'")
        
        user_response = "yes"
        is_acceptance = is_followup_acceptance(user_response)
        print(f"🤔 Is '{user_response}' an acceptance? {is_acceptance}")
        
        if is_acceptance:
            # Get the follow-up question to process
            question_to_process = get_last_followup(user_id, chat_id)
            print(f"🔄 Processing follow-up: '{question_to_process}'")
            
            # Mark as answered
            mark_followup_answered(user_id, chat_id)
            print("✅ Follow-up marked as answered")
            
            # Verify cleanup
            after_cleanup = get_last_followup(user_id, chat_id)
            print(f"🧹 After cleanup: {after_cleanup}")
            assert after_cleanup is None, "Follow-up cleanup failed"
            print("✅ Cleanup verification passed")
        print()
        
        # Scenario 3: New follow-up from ML response
        print("📝 Scenario 3: System responds about machine learning")
        
        ml_response = """
        Machine learning is a subset of AI that enables computers to learn and improve from experience without being explicitly programmed. Here are the main types:

        **Supervised Learning**: Uses labeled data to train models
        - Examples: Email spam detection, image recognition
        
        **Unsupervised Learning**: Finds patterns in unlabeled data
        - Examples: Customer segmentation, anomaly detection
        
        **Reinforcement Learning**: Learns through trial and error
        - Examples: Game playing AI, autonomous vehicles

        👉 Are you interested in learning about neural networks and deep learning?
        """
        
        followup2 = extract_followup_question(ml_response)
        print(f"🔍 New follow-up extracted: '{followup2}'")
        
        store_followup_question(user_id, chat_id, followup2)
        print("💾 New follow-up stored")
        print()
        
        # Scenario 4: User asks different question (not acceptance)
        print("📝 Scenario 4: User asks 'What is Python programming?'")
        
        new_query = "What is Python programming?"
        is_new_acceptance = is_followup_acceptance(new_query)
        print(f"🤔 Is '{new_query}' an acceptance? {is_new_acceptance}")
        
        if not is_new_acceptance:
            print("🔄 Processing as new query (follow-up remains stored)")
            stored_followup = get_last_followup(user_id, chat_id)
            print(f"📦 Stored follow-up still available: '{stored_followup}'")
        print()
        
        # Scenario 5: User later accepts the stored follow-up
        print("📝 Scenario 5: User later says 'tell me more'")
        
        later_response = "tell me more"
        is_later_acceptance = is_followup_acceptance(later_response)
        print(f"🤔 Is '{later_response}' an acceptance? {is_later_acceptance}")
        
        if is_later_acceptance:
            final_followup = get_last_followup(user_id, chat_id)
            print(f"🔄 Processing stored follow-up: '{final_followup}'")
            mark_followup_answered(user_id, chat_id)
            print("✅ Follow-up processed and cleaned up")
        print()
        
        # Test edge cases
        print("🧪 Testing edge cases...")
        
        edge_cases = [
            ("sure thing", True),
            ("okay let's go", True),
            ("yes please", True),
            ("Can you explain quantum computing?", False),
            ("No, I don't want to know", False),
            ("Maybe later", False)
        ]
        
        for query, expected in edge_cases:
            result = is_followup_acceptance(query)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{query}' -> {result} (expected {expected})")
        
        print()
        print("🎉 All workflow tests completed successfully!")
        print("✅ Follow-up question system is fully functional!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error during workflow test: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_usage():
    """Demonstrate how to use the follow-up system"""
    print("\n" + "="*60)
    print("📚 How to Use the Follow-up Question System")
    print("="*60)
    
    print("""
🔧 For Developers:

1. **Automatic Extraction**: Follow-up questions are automatically extracted from LLM responses
   - Look for the pattern: 👉 [question text]
   
2. **Storage**: Questions are stored in both Redis (fast) and MongoDB (persistent)
   - Redis: 1-hour expiration for active sessions
   - MongoDB: Permanent storage with answered status
   
3. **User Acceptance**: System detects when users accept follow-ups
   - Acceptance words: "yes", "sure", "ok", "go ahead", "tell me more", etc.
   - Short phrases are preferred to avoid false positives
   
4. **Query Replacement**: When accepted, the original query is replaced with the follow-up
   - This happens transparently in both /query and /rag_query endpoints
   
5. **Cleanup**: Answered follow-ups are automatically marked and cleaned up

👥 For Users:

1. **Ask any question**: The system will provide an answer
2. **Look for follow-ups**: Responses may include suggested follow-up questions (👉)
3. **Accept follow-ups**: Say "yes", "sure", "go ahead", etc. to explore the topic deeper
4. **Ask new questions**: You can always ask something completely different
5. **Continue conversations**: The system remembers context and follow-ups per chat session

🎯 Example Conversation:

User: "What is machine learning?"
System: "Machine learning is... 👉 Would you like to learn about neural networks?"

User: "yes"
System: [Explains neural networks] "👉 Are you interested in deep learning applications?"

User: "tell me more"
System: [Explains deep learning applications] "👉 Would you like examples of real-world AI?"

User: "What is Python?" (new topic)
System: [Explains Python programming] "👉 Would you like to see some Python examples?"
""")

if __name__ == "__main__":
    test_complete_workflow()
    demonstrate_usage()
    
    print("\n" + "="*60)
    print("🏁 Follow-up Question System Testing Complete!")
    print("="*60)
