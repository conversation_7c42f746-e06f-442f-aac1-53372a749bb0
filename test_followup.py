#!/usr/bin/env python3
"""
Test script for follow-up question functionality
"""

import sys
import os
import re
from datetime import datetime

# Add the current directory to the path to import main3 functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_extract_followup_question():
    """Test the follow-up question extraction function"""
    print("🧪 Testing follow-up question extraction...")
    
    # Test cases
    test_cases = [
        {
            "response": "This is a regular response with a follow-up.\n\n👉 Would you like me to explain more about this topic?",
            "expected": "Would you like me to explain more about this topic?"
        },
        {
            "response": "Here's some information about AI.\n\n👉 Are you interested in learning about machine learning algorithms?",
            "expected": "Are you interested in learning about machine learning algorithms?"
        },
        {
            "response": "This response has no follow-up question.",
            "expected": None
        },
        {
            "response": "Multiple lines here.\n\n👉 Do you want a deeper explanation of neural networks?\n\nSome more text.",
            "expected": "Do you want a deeper explanation of neural networks?"
        }
    ]
    
    # Import the function
    from main3 import extract_followup_question
    
    for i, test_case in enumerate(test_cases, 1):
        result = extract_followup_question(test_case["response"])
        expected = test_case["expected"]
        
        if result == expected:
            print(f"✅ Test {i}: PASSED")
            if result:
                print(f"   Extracted: '{result}'")
        else:
            print(f"❌ Test {i}: FAILED")
            print(f"   Expected: '{expected}'")
            print(f"   Got: '{result}'")
    
    print()

def test_followup_acceptance():
    """Test the follow-up acceptance detection function"""
    print("🧪 Testing follow-up acceptance detection...")
    
    # Test cases
    test_cases = [
        {"query": "yes", "expected": True},
        {"query": "Yes", "expected": True},
        {"query": "sure", "expected": True},
        {"query": "ok", "expected": True},
        {"query": "go ahead", "expected": True},
        {"query": "tell me more", "expected": True},
        {"query": "please", "expected": True},
        {"query": "What is machine learning?", "expected": False},
        {"query": "No, I don't want to know more", "expected": False},
        {"query": "Can you explain something else?", "expected": False},
        {"query": "yes please", "expected": True},
        {"query": "okay sure", "expected": True}
    ]
    
    # Import the function
    from main3 import is_followup_acceptance
    
    for i, test_case in enumerate(test_cases, 1):
        result = is_followup_acceptance(test_case["query"])
        expected = test_case["expected"]
        
        if result == expected:
            print(f"✅ Test {i}: PASSED - '{test_case['query']}' -> {result}")
        else:
            print(f"❌ Test {i}: FAILED - '{test_case['query']}'")
            print(f"   Expected: {expected}, Got: {result}")
    
    print()

def test_regex_patterns():
    """Test the regex patterns used in follow-up extraction"""
    print("🧪 Testing regex patterns...")
    
    pattern = r'👉\s*(.+?)(?:\n|$)'
    
    test_texts = [
        "👉 Would you like to know more?",
        "👉Would you like to know more?",
        "👉   Are you interested in this topic?   ",
        "Some text\n👉 Do you want details?\nMore text",
        "No follow-up here",
        "👉 Multiple\nlines\nquestion?"
    ]
    
    for text in test_texts:
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if match:
            followup = match.group(1).strip()
            print(f"✅ Found: '{followup}' in '{text[:50]}...'")
        else:
            print(f"❌ No match in: '{text[:50]}...'")
    
    print()

if __name__ == "__main__":
    print("🚀 Starting Follow-up Question Tests")
    print("=" * 50)
    
    try:
        test_extract_followup_question()
        test_followup_acceptance()
        test_regex_patterns()
        
        print("🎉 All tests completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure main3.py is in the same directory and all dependencies are installed.")
    except Exception as e:
        print(f"❌ Error during testing: {e}")
