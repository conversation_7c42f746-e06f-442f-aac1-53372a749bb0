#!/usr/bin/env python3
"""
Integration test for follow-up question functionality
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from bson import ObjectId

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_followup_integration():
    """Test the complete follow-up question workflow"""
    print("🧪 Testing Follow-up Question Integration...")
    print("=" * 50)
    
    try:
        # Import required functions
        from main3 import (
            extract_followup_question,
            store_followup_question,
            get_last_followup,
            mark_followup_answered,
            is_followup_acceptance,
            memory_manager1
        )
        
        # Test data
        test_user_id = str(ObjectId())
        test_chat_id = str(ObjectId())
        
        print(f"📝 Test User ID: {test_user_id}")
        print(f"📝 Test Chat ID: {test_chat_id}")
        print()
        
        # Test 1: Extract follow-up question from response
        print("🔍 Test 1: Extract follow-up question")
        test_response = """
        Here's some information about artificial intelligence. AI is a broad field that encompasses machine learning, natural language processing, and computer vision.
        
        👉 Would you like me to explain more about machine learning algorithms and how they work?
        """
        
        followup = extract_followup_question(test_response)
        if followup:
            print(f"✅ Extracted follow-up: '{followup}'")
        else:
            print("❌ Failed to extract follow-up question")
            return
        
        # Test 2: Store follow-up question
        print("\n💾 Test 2: Store follow-up question")
        try:
            store_followup_question(test_user_id, test_chat_id, followup)
            print("✅ Follow-up question stored successfully")
        except Exception as e:
            print(f"❌ Failed to store follow-up: {e}")
            return
        
        # Test 3: Retrieve follow-up question
        print("\n📖 Test 3: Retrieve follow-up question")
        retrieved_followup = get_last_followup(test_user_id, test_chat_id)
        if retrieved_followup == followup:
            print(f"✅ Retrieved follow-up matches: '{retrieved_followup}'")
        else:
            print(f"❌ Retrieved follow-up doesn't match")
            print(f"   Expected: '{followup}'")
            print(f"   Got: '{retrieved_followup}'")
            return
        
        # Test 4: Test follow-up acceptance detection
        print("\n🤔 Test 4: Follow-up acceptance detection")
        acceptance_tests = [
            ("yes", True),
            ("sure", True),
            ("go ahead", True),
            ("tell me more", True),
            ("What is Python?", False),
            ("No thanks", False)
        ]
        
        for query, expected in acceptance_tests:
            result = is_followup_acceptance(query)
            if result == expected:
                print(f"✅ '{query}' -> {result}")
            else:
                print(f"❌ '{query}' -> {result} (expected {expected})")
        
        # Test 5: Mark follow-up as answered
        print("\n✅ Test 5: Mark follow-up as answered")
        try:
            mark_followup_answered(test_user_id, test_chat_id)
            print("✅ Follow-up marked as answered")
            
            # Try to retrieve again (should be None or empty)
            retrieved_after_mark = get_last_followup(test_user_id, test_chat_id)
            if not retrieved_after_mark:
                print("✅ Follow-up correctly removed after marking as answered")
            else:
                print(f"⚠️ Follow-up still exists after marking as answered: '{retrieved_after_mark}'")
        except Exception as e:
            print(f"❌ Failed to mark follow-up as answered: {e}")
        
        # Test 6: Test workflow simulation
        print("\n🔄 Test 6: Workflow simulation")
        
        # Simulate storing a new follow-up
        new_followup = "Are you interested in learning about neural networks?"
        store_followup_question(test_user_id, test_chat_id, new_followup)
        print(f"📝 Stored new follow-up: '{new_followup}'")
        
        # Simulate user acceptance
        user_responses = ["yes", "sure", "go ahead", "tell me more"]
        for response in user_responses:
            if is_followup_acceptance(response):
                last_followup = get_last_followup(test_user_id, test_chat_id)
                if last_followup:
                    print(f"✅ User said '{response}' -> Retrieved: '{last_followup}'")
                    # In real scenario, this would be replaced with the follow-up question
                    break
        
        print("\n🎉 All integration tests completed successfully!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error during integration testing: {e}")
        import traceback
        traceback.print_exc()

def test_regex_edge_cases():
    """Test edge cases for follow-up extraction"""
    print("\n🔍 Testing regex edge cases...")
    
    from main3 import extract_followup_question
    
    edge_cases = [
        {
            "name": "Multiple follow-ups (should get first)",
            "text": "Some text.\n\n👉 First question?\n\nMore text.\n\n👉 Second question?",
            "expected": "First question?"
        },
        {
            "name": "Follow-up with quotes",
            "text": "Text here.\n\n👉 \"Would you like to know more about this topic?\"",
            "expected": "Would you like to know more about this topic?"
        },
        {
            "name": "Follow-up with extra spaces",
            "text": "Text here.\n\n👉    Are you interested in learning more?   ",
            "expected": "Are you interested in learning more?"
        },
        {
            "name": "No follow-up",
            "text": "Just regular text without any follow-up questions.",
            "expected": None
        },
        {
            "name": "Follow-up at end of text",
            "text": "Some information here.\n\n👉 Want to learn more?",
            "expected": "Want to learn more?"
        }
    ]
    
    for case in edge_cases:
        result = extract_followup_question(case["text"])
        if result == case["expected"]:
            print(f"✅ {case['name']}: PASSED")
        else:
            print(f"❌ {case['name']}: FAILED")
            print(f"   Expected: '{case['expected']}'")
            print(f"   Got: '{result}'")

if __name__ == "__main__":
    print("🚀 Starting Follow-up Question Integration Tests")
    print("=" * 60)
    
    # Run the async test
    asyncio.run(test_followup_integration())
    
    # Run edge case tests
    test_regex_edge_cases()
    
    print("\n" + "=" * 60)
    print("🏁 Integration testing completed!")
