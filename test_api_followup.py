#!/usr/bin/env python3
"""
API test for follow-up question functionality
"""

import asyncio
import json
import requests
import time
from bson import ObjectId

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = str(ObjectId())
TEST_CHAT_ID = str(ObjectId())

def test_api_followup_workflow():
    """Test the complete follow-up workflow via API"""
    print("🧪 Testing Follow-up Question API Workflow")
    print("=" * 50)
    
    print(f"📝 Test User ID: {TEST_USER_ID}")
    print(f"📝 Test Chat ID: {TEST_CHAT_ID}")
    print()
    
    # Test 1: Send a query that should generate a follow-up question
    print("🔍 Test 1: Send initial query")
    
    initial_query = {
        "query": "What is artificial intelligence?",
        "user_id": TEST_USER_ID,
        "chat_id": TEST_CHAT_ID,
        "mode": "rag",
        "host": "groq",
        "model": "llama-3.1-8b-instant",
        "api_key": "your-api-key-here",
        "collections": ["default"]
    }
    
    try:
        print("📤 Sending initial query...")
        response = requests.post(f"{BASE_URL}/rag_query", json=initial_query, stream=True)
        
        if response.status_code == 200:
            print("✅ Initial query sent successfully")
            
            # Collect the streaming response
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
                    print(chunk, end="", flush=True)
            
            print("\n" + "="*50)
            
            # Check if response contains a follow-up question
            if "👉" in full_response:
                print("✅ Follow-up question detected in response")
                
                # Extract the follow-up question
                import re
                pattern = r'👉\s*(.+?)(?:\n|$)'
                match = re.search(pattern, full_response, re.IGNORECASE | re.DOTALL)
                if match:
                    followup = match.group(1).strip()
                    print(f"📝 Extracted follow-up: '{followup}'")
                    
                    # Test 2: Send acceptance response
                    print("\n🔍 Test 2: Send follow-up acceptance")
                    
                    acceptance_query = {
                        "query": "yes",
                        "user_id": TEST_USER_ID,
                        "chat_id": TEST_CHAT_ID,
                        "mode": "rag",
                        "host": "groq",
                        "model": "llama-3.1-8b-instant",
                        "api_key": "your-api-key-here",
                        "collections": ["default"]
                    }
                    
                    print("📤 Sending acceptance response...")
                    time.sleep(2)  # Wait a bit before sending the next request
                    
                    acceptance_response = requests.post(f"{BASE_URL}/rag_query", json=acceptance_query, stream=True)
                    
                    if acceptance_response.status_code == 200:
                        print("✅ Acceptance query sent successfully")
                        
                        # Collect the streaming response
                        acceptance_full_response = ""
                        for chunk in acceptance_response.iter_content(chunk_size=1024, decode_unicode=True):
                            if chunk:
                                acceptance_full_response += chunk
                                print(chunk, end="", flush=True)
                        
                        print("\n" + "="*50)
                        print("✅ Follow-up workflow test completed!")
                        
                    else:
                        print(f"❌ Acceptance query failed: {acceptance_response.status_code}")
                        print(acceptance_response.text)
                else:
                    print("❌ Could not extract follow-up question from response")
            else:
                print("⚠️ No follow-up question found in response")
                
        else:
            print(f"❌ Initial query failed: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error during API test: {e}")

def test_simple_acceptance_patterns():
    """Test simple acceptance patterns without full API"""
    print("\n🔍 Testing acceptance patterns...")
    
    # Import the function directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from main3 import is_followup_acceptance
        
        test_cases = [
            "yes",
            "sure",
            "ok",
            "go ahead",
            "tell me more",
            "please",
            "What is machine learning?",  # Should be False
            "No thanks"  # Should be False
        ]
        
        for query in test_cases:
            result = is_followup_acceptance(query)
            print(f"  '{query}' -> {result}")
            
    except ImportError:
        print("❌ Could not import functions for testing")

if __name__ == "__main__":
    print("🚀 Starting API Follow-up Question Tests")
    print("=" * 60)
    
    # Test acceptance patterns first (doesn't require server)
    test_simple_acceptance_patterns()
    
    # Test full API workflow (requires server to be running)
    print("\n" + "="*60)
    test_api_followup_workflow()
    
    print("\n" + "="*60)
    print("🏁 API testing completed!")
    print("\nNote: For full API testing, make sure:")
    print("1. The server is running: python main3.py")
    print("2. You have valid API keys configured")
    print("3. The server is accessible at http://localhost:8000")
